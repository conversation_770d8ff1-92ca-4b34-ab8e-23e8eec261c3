import React, { createContext, useContext, useState, useEffect } from 'react';
import { dataService } from '../services/dataService';
import { queryService } from '../services/queryService';
import { useAuth } from './AuthContext';

const DataContext = createContext();

export const useData = () => {
  const context = useContext(DataContext);
  if (!context) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
};

export const DataProvider = ({ children }) => {
  const { isAuthenticated } = useAuth();
  const [files, setFiles] = useState([]);
  const [tables, setTables] = useState([]);
  const [loading, setLoading] = useState(false);
  const [queryHistory, setQueryHistory] = useState([]);

  // Load user files and tables
  const loadFiles = async () => {
    if (!isAuthenticated) return;
    
    setLoading(true);
    try {
      const filesData = await dataService.getFiles();
      setFiles(filesData.files);
      
      const tablesData = await queryService.getTables();
      setTables(tablesData.tables);
    } catch (error) {
      console.error('Failed to load files:', error);
    } finally {
      setLoading(false);
    }
  };

  // Upload file
  const uploadFile = async (file) => {
    try {
      const response = await dataService.uploadFile(file);
      await loadFiles(); // Refresh files list
      return { success: true, data: response };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.error || 'Upload failed' 
      };
    }
  };

  // Delete file
  const deleteFile = async (fileId) => {
    try {
      await dataService.deleteFile(fileId);
      await loadFiles(); // Refresh files list
      return { success: true };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.error || 'Delete failed' 
      };
    }
  };

  // Execute SQL query
  const executeQuery = async (query) => {
    try {
      const response = await queryService.executeQuery(query);
      
      // Add to query history if successful
      if (response.success) {
        setQueryHistory(prev => [
          {
            query,
            timestamp: new Date().toISOString(),
            execution_time: response.execution_time,
            row_count: response.row_count
          },
          ...prev.slice(0, 49) // Keep last 50 queries
        ]);
      }
      
      return response;
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.error || 'Query execution failed' 
      };
    }
  };

  // Get query history from server
  const loadQueryHistory = async () => {
    if (!isAuthenticated) return;
    
    try {
      const response = await queryService.getQueryHistory();
      setQueryHistory(response.history);
    } catch (error) {
      console.error('Failed to load query history:', error);
    }
  };

  // Preview file data
  const previewFile = async (fileId) => {
    try {
      const response = await dataService.previewFile(fileId);
      return { success: true, data: response };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.error || 'Preview failed' 
      };
    }
  };

  // Get table schema
  const getTableSchema = async (tableName) => {
    try {
      const response = await queryService.getTableSchema(tableName);
      return { success: true, data: response };
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.error || 'Failed to get schema' 
      };
    }
  };

  // Load data when authenticated
  useEffect(() => {
    if (isAuthenticated) {
      loadFiles();
      loadQueryHistory();
    } else {
      setFiles([]);
      setTables([]);
      setQueryHistory([]);
    }
  }, [isAuthenticated]);

  const value = {
    files,
    tables,
    loading,
    queryHistory,
    uploadFile,
    deleteFile,
    executeQuery,
    previewFile,
    getTableSchema,
    loadFiles,
    loadQueryHistory,
  };

  return (
    <DataContext.Provider value={value}>
      {children}
    </DataContext.Provider>
  );
};
