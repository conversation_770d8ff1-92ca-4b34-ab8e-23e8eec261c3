import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { AuthProvider } from './contexts/AuthContext';
import { DataProvider } from './contexts/DataContext';
import Layout from './components/Layout/Layout';
import Login from './components/Auth/Login';
import Register from './components/Auth/Register';
import Dashboard from './components/Dashboard/Dashboard';
import SQLWorkbench from './components/SQLWorkbench/SQLWorkbench';
import FileManager from './components/FileManager/FileManager';
import Notebooks from './components/Notebooks/Notebooks';
import Visualizations from './components/Visualizations/Visualizations';
import ProtectedRoute from './components/Auth/ProtectedRoute';

// Material-UI theme with blue color scheme
const theme = createTheme({
  palette: {
    primary: {
      main: '#757de8',
      light: '#a5a9f0',
      dark: '#5c6bc0',
      contrastText: '#ffffff',
    },
    secondary: {
      main: '#2196f3',
      light: '#64b5f6',
      dark: '#1976d2',
      contrastText: '#ffffff',
    },
    background: {
      default: '#fafafa',
      paper: '#ffffff',
    },
    text: {
      primary: '#333333',
      secondary: '#666666',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h4: {
      fontWeight: 600,
    },
    h5: {
      fontWeight: 500,
    },
    h6: {
      fontWeight: 500,
    },
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          borderRadius: 8,
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: 8,
        },
      },
    },
  },
});

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AuthProvider>
        <Router>
          <Routes>
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />
            <Route path="/test" element={
              <div style={{ padding: '20px', textAlign: 'center' }}>
                <h1 style={{ color: theme.palette.primary.main }}>🚀 Hamarttan Test Page</h1>
                <p>React Router is working!</p>
                <p>AuthProvider is working!</p>
              </div>
            } />
            <Route path="/" element={
              <div style={{ padding: '20px', textAlign: 'center' }}>
                <h1 style={{ color: theme.palette.primary.main }}>🚀 Hamarttan is Loading!</h1>
                <p>If you see this message, the React app is working correctly!</p>
                <p>Backend API Status: <span style={{ color: 'green' }}>✅ Connected</span></p>
                <p><a href="/test">Test Router</a> | <a href="/login">Login</a></p>
              </div>
            } />
          </Routes>
        </Router>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;
