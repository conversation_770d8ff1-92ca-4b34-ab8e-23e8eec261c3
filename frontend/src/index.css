/* Reset and base styles for Hamarttan */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  min-height: 100vh;
}

/* Remove default button styles to let Material-UI handle them */
button {
  font-family: inherit;
}

/* Ensure proper text rendering */
* {
  text-rendering: optimizeLegibility;
}
