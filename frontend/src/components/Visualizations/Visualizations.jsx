import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Alert,
  Grid,
  Card,
  CardContent,
  CardMedia,
  CardActions,
  Tabs,
  Tab,
} from '@mui/material';
import Editor from '@monaco-editor/react';
import { <PERSON><PERSON><PERSON>, PlayArrow } from '@mui/icons-material';
import { vizService } from '../../services/vizService';

const Visualizations = () => {
  const [query, setQuery] = useState(`-- Create visualizations with custom SQL functions
-- Available functions: HISTOGRAM, BOXPLOT, SCATTER, LINE, BAR

SELECT HISTOGRAM(column_name, 'Chart Title') 
FROM your_table_name;`);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [plotUrl, setPlotUrl] = useState('');
  const [tabValue, setTabValue] = useState(0);

  const handleGenerateVisualization = async () => {
    if (!query.trim()) {
      setError('Please enter a visualization query');
      return;
    }

    setLoading(true);
    setError('');
    setPlotUrl('');

    try {
      const result = await vizService.generateVisualization(query);
      if (result.success) {
        setPlotUrl(result.plot_url);
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError('Failed to generate visualization');
    }
    
    setLoading(false);
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const exampleQueries = [
    {
      title: 'Histogram',
      query: `SELECT HISTOGRAM(age, 'Age Distribution') 
FROM demographics;`,
    },
    {
      title: 'Scatter Plot',
      query: `SELECT SCATTER(height, weight, 'Height vs Weight') 
FROM measurements;`,
    },
    {
      title: 'Box Plot',
      query: `SELECT BOXPLOT(salary, department, 'Salary by Department') 
FROM employees;`,
    },
  ];

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Visualizations
      </Typography>

      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 2 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h6">
                Visualization Query
              </Typography>
              <Button
                variant="contained"
                startIcon={<PlayArrow />}
                onClick={handleGenerateVisualization}
                disabled={loading}
              >
                {loading ? 'Generating...' : 'Generate Plot'}
              </Button>
            </Box>

            <Box mb={2} sx={{ border: '1px solid #e0e0e0', borderRadius: 1 }}>
              <Editor
                height="200px"
                defaultLanguage="sql"
                value={query}
                onChange={(value) => setQuery(value || '')}
                options={{
                  minimap: { enabled: false },
                  fontSize: 14,
                  lineNumbers: 'on',
                  roundedSelection: false,
                  scrollBeyondLastLine: false,
                  automaticLayout: true,
                }}
              />
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}

            {plotUrl && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Generated Visualization
                </Typography>
                <Paper sx={{ p: 2, textAlign: 'center' }}>
                  <img 
                    src={plotUrl} 
                    alt="Generated Plot" 
                    style={{ maxWidth: '100%', height: 'auto' }}
                  />
                </Paper>
              </Box>
            )}

            {!plotUrl && !loading && (
              <Paper sx={{ p: 3, textAlign: 'center', bgcolor: 'grey.50' }}>
                <BarChart sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                <Typography variant="body1" color="textSecondary">
                  Enter a visualization query above and click "Generate Plot" to create your chart.
                </Typography>
              </Paper>
            )}
          </Paper>
        </Grid>

        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2 }}>
            <Tabs value={tabValue} onChange={handleTabChange} sx={{ mb: 2 }}>
              <Tab label="Examples" />
              <Tab label="Functions" />
            </Tabs>

            {tabValue === 0 && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Example Queries
                </Typography>
                {exampleQueries.map((example, index) => (
                  <Card key={index} sx={{ mb: 2 }}>
                    <CardContent>
                      <Typography variant="subtitle2" gutterBottom>
                        {example.title}
                      </Typography>
                      <Typography variant="body2" component="pre" sx={{ fontSize: '0.75rem' }}>
                        {example.query}
                      </Typography>
                    </CardContent>
                    <CardActions>
                      <Button 
                        size="small" 
                        onClick={() => setQuery(example.query)}
                      >
                        Use This Query
                      </Button>
                    </CardActions>
                  </Card>
                ))}
              </Box>
            )}

            {tabValue === 1 && (
              <Box>
                <Typography variant="h6" gutterBottom>
                  Available Functions
                </Typography>
                <Box>
                  {[
                    'HISTOGRAM(column, title)',
                    'BOXPLOT(column, group, title)',
                    'SCATTER(x_col, y_col, title)',
                    'LINE(x_col, y_col, title)',
                    'BAR(category, value, title)',
                  ].map((func, index) => (
                    <Typography key={index} variant="body2" component="div" sx={{ mb: 1 }}>
                      <code>{func}</code>
                    </Typography>
                  ))}
                </Box>
                <Alert severity="info" sx={{ mt: 2 }}>
                  These custom SQL functions generate ggplot2 visualizations using your data.
                </Alert>
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Visualizations;
