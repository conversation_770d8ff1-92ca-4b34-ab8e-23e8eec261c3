import React from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Paper,
  List,
  ListItem,
  ListItemText,
  Chip,
} from '@mui/material';
import {
  Storage as StorageIcon,
  Code as CodeIcon,
  BarChart as BarChartIcon,
  Book as BookIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useData } from '../../contexts/DataContext';
import { useAuth } from '../../contexts/AuthContext';

const Dashboard = () => {
  const navigate = useNavigate();
  const { files, tables, queryHistory } = useData();
  const { user } = useAuth();

  const quickActions = [
    {
      title: 'Upload Data',
      description: 'Upload CSV, Excel, or other data files',
      icon: <StorageIcon />,
      action: () => navigate('/files'),
      color: 'primary',
    },
    {
      title: 'SQL Query',
      description: 'Run SQL queries on your data',
      icon: <CodeIcon />,
      action: () => navigate('/sql'),
      color: 'secondary',
    },
    {
      title: 'Create Visualization',
      description: 'Generate charts and plots',
      icon: <BarChartIcon />,
      action: () => navigate('/visualizations'),
      color: 'success',
    },
    {
      title: 'New Notebook',
      description: 'Create Python or R notebook',
      icon: <BookIcon />,
      action: () => navigate('/notebooks'),
      color: 'warning',
    },
  ];

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Welcome back, {user?.first_name}!
      </Typography>
      <Typography variant="body1" color="textSecondary" paragraph>
        Your data analysis workspace is ready. Get started by uploading data or running queries.
      </Typography>

      <Grid container spacing={3}>
        {/* Quick Actions */}
        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom>
            Quick Actions
          </Typography>
          <Grid container spacing={2}>
            {quickActions.map((action, index) => (
              <Grid item xs={12} sm={6} md={3} key={index}>
                <Card 
                  sx={{ 
                    cursor: 'pointer',
                    transition: 'transform 0.2s',
                    '&:hover': {
                      transform: 'translateY(-2px)',
                    },
                  }}
                  onClick={action.action}
                >
                  <CardContent>
                    <Box display="flex" alignItems="center" mb={1}>
                      <Box color={`${action.color}.main`} mr={1}>
                        {action.icon}
                      </Box>
                      <Typography variant="h6">
                        {action.title}
                      </Typography>
                    </Box>
                    <Typography variant="body2" color="textSecondary">
                      {action.description}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Grid>

        {/* Data Overview */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Your Data Files
            </Typography>
            {files.length === 0 ? (
              <Typography variant="body2" color="textSecondary">
                No files uploaded yet. Start by uploading your first dataset.
              </Typography>
            ) : (
              <List dense>
                {files.slice(0, 5).map((file) => (
                  <ListItem key={file.id}>
                    <ListItemText
                      primary={file.original_filename}
                      secondary={`${(file.file_size / 1024 / 1024).toFixed(2)} MB • ${file.file_type.toUpperCase()}`}
                    />
                    <Chip 
                      label={file.table_name} 
                      size="small" 
                      color="primary" 
                      variant="outlined" 
                    />
                  </ListItem>
                ))}
                {files.length > 5 && (
                  <ListItem>
                    <Button 
                      variant="text" 
                      onClick={() => navigate('/files')}
                      size="small"
                    >
                      View all {files.length} files
                    </Button>
                  </ListItem>
                )}
              </List>
            )}
          </Paper>
        </Grid>

        {/* Recent Queries */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Recent Queries
            </Typography>
            {queryHistory.length === 0 ? (
              <Typography variant="body2" color="textSecondary">
                No queries executed yet. Try running your first SQL query.
              </Typography>
            ) : (
              <List dense>
                {queryHistory.slice(0, 3).map((query, index) => (
                  <ListItem key={index}>
                    <ListItemText
                      primary={query.query_text.length > 50 
                        ? `${query.query_text.substring(0, 50)}...` 
                        : query.query_text
                      }
                      secondary={`${query.execution_time}s • ${query.result_count} rows`}
                    />
                  </ListItem>
                ))}
                {queryHistory.length > 3 && (
                  <ListItem>
                    <Button 
                      variant="text" 
                      onClick={() => navigate('/sql')}
                      size="small"
                    >
                      View query history
                    </Button>
                  </ListItem>
                )}
              </List>
            )}
          </Paper>
        </Grid>

        {/* Statistics */}
        <Grid item xs={12}>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={4}>
              <Card>
                <CardContent>
                  <Typography variant="h4" color="primary">
                    {files.length}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Data Files
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Card>
                <CardContent>
                  <Typography variant="h4" color="secondary">
                    {tables.length}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Available Tables
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Card>
                <CardContent>
                  <Typography variant="h4" color="success.main">
                    {queryHistory.length}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Queries Executed
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
