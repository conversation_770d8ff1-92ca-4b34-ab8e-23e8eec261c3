import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  LinearProgress,
} from '@mui/material';
import {
  CloudUpload,
  Delete,
  Download,
  Visibility,
} from '@mui/icons-material';
import { useData } from '../../contexts/DataContext';

const FileManager = () => {
  const { files, uploadFile, deleteFile, previewFile, loading } = useData();
  const [uploading, setUploading] = useState(false);
  const [uploadError, setUploadError] = useState('');
  const [previewDialog, setPreviewDialog] = useState({ open: false, data: null });

  const handleFileUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    setUploading(true);
    setUploadError('');

    const result = await uploadFile(file);
    
    if (!result.success) {
      setUploadError(result.error);
    }
    
    setUploading(false);
    event.target.value = ''; // Reset file input
  };

  const handleDelete = async (fileId) => {
    if (window.confirm('Are you sure you want to delete this file?')) {
      await deleteFile(fileId);
    }
  };

  const handlePreview = async (fileId) => {
    const result = await previewFile(fileId);
    if (result.success) {
      setPreviewDialog({ open: true, data: result.data });
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">
          File Manager
        </Typography>
        <Button
          variant="contained"
          component="label"
          startIcon={<CloudUpload />}
          disabled={uploading}
        >
          Upload File
          <input
            type="file"
            hidden
            accept=".csv,.xlsx,.xls,.dta,.sas7bdat,.xpt,.parquet"
            onChange={handleFileUpload}
          />
        </Button>
      </Box>

      {uploading && (
        <Box mb={2}>
          <LinearProgress />
          <Typography variant="body2" color="textSecondary" mt={1}>
            Uploading and converting file...
          </Typography>
        </Box>
      )}

      {uploadError && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {uploadError}
        </Alert>
      )}

      <Paper>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>File Name</TableCell>
                <TableCell>Table Name</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Size</TableCell>
                <TableCell>Upload Date</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {files.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    <Typography variant="body2" color="textSecondary">
                      No files uploaded yet. Upload your first dataset to get started.
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                files.map((file) => (
                  <TableRow key={file.id}>
                    <TableCell>{file.original_filename}</TableCell>
                    <TableCell>
                      <code>{file.table_name}</code>
                    </TableCell>
                    <TableCell>{file.file_type.toUpperCase()}</TableCell>
                    <TableCell>{formatFileSize(file.file_size)}</TableCell>
                    <TableCell>
                      {new Date(file.upload_date).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      <IconButton
                        size="small"
                        onClick={() => handlePreview(file.id)}
                        title="Preview"
                      >
                        <Visibility />
                      </IconButton>
                      <IconButton
                        size="small"
                        title="Download"
                        disabled
                      >
                        <Download />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => handleDelete(file.id)}
                        title="Delete"
                        color="error"
                      >
                        <Delete />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      {/* Preview Dialog */}
      <Dialog
        open={previewDialog.open}
        onClose={() => setPreviewDialog({ open: false, data: null })}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>File Preview</DialogTitle>
        <DialogContent>
          {previewDialog.data && (
            <Box>
              <Typography variant="body2" gutterBottom>
                Showing first {previewDialog.data.preview_rows} of {previewDialog.data.total_rows} rows
              </Typography>
              <TableContainer component={Paper} sx={{ mt: 2 }}>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      {previewDialog.data.columns.map((col) => (
                        <TableCell key={col}>{col}</TableCell>
                      ))}
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {previewDialog.data.data.map((row, index) => (
                      <TableRow key={index}>
                        {previewDialog.data.columns.map((col) => (
                          <TableCell key={col}>
                            {row[col]?.toString() || ''}
                          </TableCell>
                        ))}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPreviewDialog({ open: false, data: null })}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default FileManager;
