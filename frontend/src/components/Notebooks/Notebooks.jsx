import React from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActions,
  Chip,
} from '@mui/material';
import {
  Add,
  Book,
  Code,
} from '@mui/icons-material';

const Notebooks = () => {
  const mockNotebooks = [
    {
      id: 1,
      name: 'Data Exploration',
      kernel: 'python',
      lastModified: '2024-01-15',
      cellCount: 12,
    },
    {
      id: 2,
      name: 'Statistical Analysis',
      kernel: 'r',
      lastModified: '2024-01-14',
      cellCount: 8,
    },
  ];

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">
          Notebooks
        </Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          disabled
        >
          New Notebook
        </Button>
      </Box>

      <Paper sx={{ p: 3, textAlign: 'center', mb: 3 }}>
        <Book sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
        <Typography variant="h6" gutterBottom>
          Jupyter-style Notebooks Coming Soon
        </Typography>
        <Typography variant="body2" color="textSecondary" paragraph>
          Create and run interactive notebooks with Python and R kernels for advanced data analysis.
          This feature is currently under development.
        </Typography>
      </Paper>

      <Typography variant="h6" gutterBottom>
        Recent Notebooks (Preview)
      </Typography>
      
      <Grid container spacing={2}>
        {mockNotebooks.map((notebook) => (
          <Grid item xs={12} sm={6} md={4} key={notebook.id}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" mb={1}>
                  <Code sx={{ mr: 1, color: 'text.secondary' }} />
                  <Typography variant="h6">
                    {notebook.name}
                  </Typography>
                </Box>
                <Box mb={2}>
                  <Chip
                    label={notebook.kernel.toUpperCase()}
                    size="small"
                    color={notebook.kernel === 'python' ? 'primary' : 'secondary'}
                  />
                </Box>
                <Typography variant="body2" color="textSecondary">
                  {notebook.cellCount} cells • Modified {notebook.lastModified}
                </Typography>
              </CardContent>
              <CardActions>
                <Button size="small" disabled>
                  Open
                </Button>
                <Button size="small" disabled>
                  Delete
                </Button>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default Notebooks;
