import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  Grid,
} from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import Editor from '@monaco-editor/react';
import { PlayArrow, Save, History } from '@mui/icons-material';
import { useData } from '../../contexts/DataContext';

const SQLWorkbench = () => {
  const { executeQuery, tables, queryHistory } = useData();
  const [query, setQuery] = useState('-- Welcome to Hamarttan SQL Workbench\n-- Your tables are available for querying\n\nSELECT * FROM your_table_name LIMIT 10;');
  const [results, setResults] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [tabValue, setTabValue] = useState(0);

  const handleExecuteQuery = async () => {
    if (!query.trim()) {
      setError('Please enter a SQL query');
      return;
    }

    setLoading(true);
    setError('');
    setResults(null);

    const result = await executeQuery(query);
    
    if (result.success) {
      setResults(result);
    } else {
      setError(result.error);
    }
    
    setLoading(false);
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const formatColumns = (columns) => {
    return columns.map((col) => ({
      field: col,
      headerName: col,
      width: 150,
      sortable: true,
    }));
  };

  const formatRows = (data) => {
    return data.map((row, index) => ({
      id: index,
      ...row,
    }));
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        SQL Workbench
      </Typography>
      
      <Grid container spacing={3}>
        {/* Left Panel - Tables */}
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 2, height: 'fit-content' }}>
            <Typography variant="h6" gutterBottom>
              Available Tables
            </Typography>
            {tables.length === 0 ? (
              <Typography variant="body2" color="textSecondary">
                No tables available. Upload some data files first.
              </Typography>
            ) : (
              <Box>
                {tables.map((table) => (
                  <Box key={table.table_name} mb={1}>
                    <Typography 
                      variant="body2" 
                      sx={{ 
                        cursor: 'pointer',
                        '&:hover': { color: 'primary.main' }
                      }}
                      onClick={() => setQuery(`SELECT * FROM ${table.table_name} LIMIT 10;`)}
                    >
                      📊 {table.table_name}
                    </Typography>
                    <Typography variant="caption" color="textSecondary">
                      {table.original_filename}
                    </Typography>
                  </Box>
                ))}
              </Box>
            )}
          </Paper>
        </Grid>

        {/* Main Panel */}
        <Grid item xs={12} md={9}>
          <Paper sx={{ p: 2 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h6">
                Query Editor
              </Typography>
              <Box>
                <Button
                  variant="contained"
                  startIcon={<PlayArrow />}
                  onClick={handleExecuteQuery}
                  disabled={loading}
                  sx={{ mr: 1 }}
                >
                  {loading ? 'Executing...' : 'Execute'}
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<Save />}
                  disabled
                >
                  Save
                </Button>
              </Box>
            </Box>

            {/* SQL Editor */}
            <Box mb={2} sx={{ border: '1px solid #e0e0e0', borderRadius: 1 }}>
              <Editor
                height="200px"
                defaultLanguage="sql"
                value={query}
                onChange={(value) => setQuery(value || '')}
                options={{
                  minimap: { enabled: false },
                  fontSize: 14,
                  lineNumbers: 'on',
                  roundedSelection: false,
                  scrollBeyondLastLine: false,
                  automaticLayout: true,
                }}
              />
            </Box>

            {/* Error Display */}
            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}

            {/* Loading */}
            {loading && (
              <Box display="flex" justifyContent="center" my={2}>
                <CircularProgress />
              </Box>
            )}

            {/* Results */}
            {results && (
              <Box>
                <Tabs value={tabValue} onChange={handleTabChange} sx={{ mb: 2 }}>
                  <Tab label={`Results (${results.row_count} rows)`} />
                  <Tab label="Query Info" />
                </Tabs>

                {tabValue === 0 && (
                  <Box sx={{ height: 400, width: '100%' }}>
                    <DataGrid
                      rows={formatRows(results.data)}
                      columns={formatColumns(results.columns)}
                      pageSize={25}
                      rowsPerPageOptions={[25, 50, 100]}
                      disableSelectionOnClick
                      density="compact"
                    />
                  </Box>
                )}

                {tabValue === 1 && (
                  <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                    <Typography variant="body2">
                      <strong>Execution Time:</strong> {results.execution_time}s
                    </Typography>
                    <Typography variant="body2">
                      <strong>Rows Returned:</strong> {results.row_count}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Columns:</strong> {results.columns.join(', ')}
                    </Typography>
                  </Paper>
                )}
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default SQLWorkbench;
