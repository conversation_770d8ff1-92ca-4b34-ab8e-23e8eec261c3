import api from './authService';

export const vizService = {
  async generateVisualization(query) {
    const response = await api.post('/viz/generate', { query });
    return response.data;
  },

  async getSupportedFunctions() {
    const response = await api.get('/viz/supported-functions');
    return response.data;
  },

  async getColorThemes() {
    const response = await api.get('/viz/color-themes');
    return response.data;
  },

  async getExamples() {
    const response = await api.get('/viz/examples');
    return response.data;
  },

  async getPlot(plotId) {
    const response = await api.get(`/viz/plot/${plotId}`, {
      responseType: 'blob',
    });
    return response.data;
  },

  async deletePlot(plotId) {
    const response = await api.delete(`/viz/plot/${plotId}`);
    return response.data;
  },
};
