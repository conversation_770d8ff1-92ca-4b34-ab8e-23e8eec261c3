import api from './authService';

export const queryService = {
  async executeQuery(query) {
    const response = await api.post('/query/execute', { query });
    return response.data;
  },

  async validateQuery(query) {
    const response = await api.post('/query/validate', { query });
    return response.data;
  },

  async getTables() {
    const response = await api.get('/query/tables');
    return response.data;
  },

  async getTableSchema(tableName) {
    const response = await api.get(`/query/tables/${tableName}/schema`);
    return response.data;
  },

  async getQueryHistory(limit = 50) {
    const response = await api.get(`/query/history?limit=${limit}`);
    return response.data;
  },
};
