import api from './authService';

export const dataService = {
  async uploadFile(file) {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await api.post('/data/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  async getFiles() {
    const response = await api.get('/data/files');
    return response.data;
  },

  async deleteFile(fileId) {
    const response = await api.delete(`/data/files/${fileId}`);
    return response.data;
  },

  async downloadFile(fileId) {
    const response = await api.get(`/data/files/${fileId}/download`, {
      responseType: 'blob',
    });
    return response.data;
  },

  async previewFile(fileId) {
    const response = await api.get(`/data/files/${fileId}/preview`);
    return response.data;
  },
};
