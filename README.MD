Hamarttan - App Concept
1. Core Idea
Hamarttan is a web-based suite of data analysis tools, acting as a user-friendly wrapper for DuckDB. It provides an integrated environment with SQL capabilities and Jupyter-style notebooks (supporting R and Python) to analyze data stored in a simple, file-based system.

2. Problem
Data analysts and scientists often need to quickly query and analyze data from various file formats (like CSVs, Excel, Stata, etc.). Setting up local databases, managing dependencies for different languages (R, Python), and converting files can be cumbersome and time-consuming, creating a barrier to rapid exploration and analysis.

3. Solution
Hamarttan provides a seamless web interface where users can sign up for free and immediately start analyzing data. Users upload their data files (e.g., CSV, Excel, SAS), which <PERSON><PERSON><PERSON> automatically converts into efficient Parquet files. These files are stored in a personal, user-specific folder and presented as a database, ready to be queried with SQL directly in the UI or analyzed further in a built-in R/Python notebook environment.

4. Target Audience
Data Analysts

Data Scientists

Students and Academics

Researchers who need to perform ad-hoc data exploration without complex setup.

5. Key Features
SQL Workbench: A primary UI view with a multi-tab SQL editor for running DuckDB queries against user files. The bottom panel will display query results.

Automatic Data Conversion: On upload, automatically converts various file types (CSV, Parquet, DTA, RData, RDS, SAS7BDAT, XPT, and individual Excel sheets) into Parquet files, treating them as tables.

Integrated Notebooks: Users can create and manage Jupyter-like notebooks with support for both R and Python kernels for more advanced analysis.

File-System as a Database: A left-side accordion menu presents the user's personal file system (a folder named by their UUID) as a database, allowing them to easily see and manage their "tables" (Parquet files).

Custom Visualizations: A dedicated system for generating visualizations based on custom query rules defined in VizQueries.MD, including support for dynamic WHERE and LIMIT clauses.

User Management: A secure user sign-up and login system, with each user having a unique UUID to manage their isolated data and resources.

6. Technical Stack & Design
Backend: Flask

Frontend: React

UI Library: Material-UI

Application DB: SQLite (for user management, with plans to migrate to PostgreSQL)

Core Engine: DuckDB (for SQL queries on Parquet files)

Design Philosophy: Clean, minimalist, modern aesthetic.

Color Theme: Light shades of blue from the Material-UI palette, with darker, complementary blues used for emphasis.

File stucture for the flask app

app.py in root, 
folders:
- controllers
- models
- routes
- assets (instead of static, with path of /assets)
- views (instead of templates)
- utils
- dbs
- downloads
- uploads
