Visualization Queries
====================

All visualization queries follow a two-step process:

Data Fetching (DuckDB): A standard SQL query is constructed to select the necessary columns from the specified table. The user's WHERE and LIMIT clauses are applied at this stage to filter the data before visualization.

Plot Generation (R + ggplot2): The resulting data frame from step 1 is passed to an R environment. The corresponding ggplot2 code is then executed to generate the final plot.

Example Flow:
User Query: SELECT HISTOGRAM(age) FROM users WHERE country = 'Canada' LIMIT 100

DuckDB executes: SELECT age FROM users WHERE country = 'Canada' LIMIT 100

R receives the resulting 100-row data frame (let's call it df).

R executes: df |> ggplot() + aes(age) + geom_histogram(fill = '#757de8', color='white')

2. Theming & Colors
Single-Color Plots: Will use a default Material-UI blue shade (e.g., #757de8).

Multi-Color Plots (with GROUP BY): Will use a sequential or qualitative color palette derived from Material-UI blue shades to ensure a consistent and modern look.

Chart Elements: Elements like histogram bars will have a white border for better visual separation and clarity.

3. Supported Visualization Functions

--------------------------------------------------------------------



for any visualization query with resulting more than 1 color create a color theme from material ui that will be used


```sql
SELECT BOXPLOT(NumericColumn) FROM table WHERE column = 'value' LIMIT 10
--converted to ggplot
--df |> ggplot() + aes(NumericColumn) + geom_boxplot(fill='#757de8')
```


other custom queries will be:

```sql
SELECT HIST(NumericColumn) FROM table WHERE column = 'value' LIMIT 10
--converted to ggplot 
--df |> ggplot() + aes(NumericColumn) + geom_histogram(fill='#757de8',color='white')
```

```sql
SELECT HISTOGRAM(NumericColumn) FROM table WHERE column = 'value' LIMIT 10
--converted to ggplot 
--df |> ggplot() + aes(NumericColumn) + geom_histogram(fill = '#757de8', color='white')
```

```sql
SELECT DENSITY(NumericColumn) FROM table WHERE column = 'value' LIMIT 10
--converted to ggplot 
--df |> ggplot() + aes(NumericColumn) + geom_density(fill='#757de8',   color='white')
```

```sql
SELECT SCATTER(NumColumn1, NumColumn2) FROM table WHERE column = 'value' LIMIT 10
--converted to ggplot 
--df |> ggplot() + aes(NumColumn1, NumColumn2) + geom_point(color='#757de8') + geom_smooth(method='lm', color='black')
```

```sql
SELECT VIOLIN(NumColumn1, NumColumn2) FROM table WHERE column = 'value' LIMIT 10
--converted to ggplot 
--df |> ggplot() + aes(NumColumn1, NumColumn2) + geom_violin(fill='#757de8',color='white')
```

```sql
SELECT BARCHART(CategoricalColumn) FROM table WHERE column = 'value' LIMIT 10
--converted to ggplot 
--df |> ggplot() + aes(CategoricalColumn) + geom_bar(fill='#757de8',color='white')
```

```sql
SELECT PIE(CategoricalColumn) FROM table WHERE column = 'value' LIMIT 10
--converted to ggplot 
--df |> group_by(CategoricalColumn) |>  summarise(Frequency=n()) |> 
--ggplot(aes(x=Frequency, y='', label=Frequency, fill=CategoricalColumn)) + geom_col(col='white') + coord_polar('x', start=0) + geom_text(position=position_stack(vjust=0.5), col='white') + theme(axis.text = element_blank(), axis.ticks = element_blank()) + labs(x='', y='')
--use a color theme with shades of blue preferrably from material ui
```

------------------------------------


```sql
SELECT BOXPLOT(NumericColumn) FROM table GROUP BY CategoricalColumn
--converted to ggplot
--df |> ggplot() + aes(NumericColumn, fill=CategoricalColumn) + geom_boxplot()
--use thme shades of blue from material ui
```

```sql
SELECT HIST(NumericColumn) FROM table GROUP BY CategoricalColumn
--converted to ggplot 
--df |> ggplot() + aes(NumericColumn, fill=CategoricalColumn) + geom_histogram(color='white')
```

```sql
SELECT HISTOGRAM(NumericColumn) FROM table WHERE column = 'value' LIMIT 10
--converted to ggplot 
--df |> ggplot() + aes(NumericColumn, fill=CategoricalColumn) + geom_histogram(color='white')
```

```sql
SELECT DENSITY(NumericColumn) FROM table GROUP BY CategoricalColumn 
--converted to ggplot 
--df |> ggplot() + aes(NumericColumn, fill= CategoricalColumn) + geom_density(color='white')
```

```sql
SELECT SCATTER(NumColumn1, NumColumn2) FROM table GROUP BY CategoricalColumn
--converted to ggplot 
--df |> ggplot() + aes(NumColumn1, NumColumn2, fill=CategoricalColumn) + geom_point()
```

```sql
SELECT VIOLIN(NumColumn1, NUmColumn2) FROM table GROUP BY CategoricalColumn
--converted to ggplot 
--df |> ggplot() + aes(NumColumn1, NumColumn2, fill=CategoricalColumn) + geom_violin(fill='#757de8',color='white')
```

```sql
SELECT BARCHART(CategoricalColumn1) FROM table GROUP BY CategoricalColumn2
--converted to ggplot 
--df |> ggplot() + aes(CategoricalColumn1, by=CategoricalColumn2) + geom_bar(position='dodge', color='white')
```
